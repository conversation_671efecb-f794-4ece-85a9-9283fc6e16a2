<%@page pageEncoding="ISO-8859-1" %>
         <a4j:outputPanel>
        <rich:modalPanel id="modalConfirmaEstorno" autosized="true" width="400" height="130"  shadowOpacity="true">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Confirmação"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkmodalConfirmaEstorno"/>
                    <rich:componentControl for="modalConfirmaEstorno" attachTo="hidelinkmodalConfirmaEstorno" operation="hide"  event="onclick" />
                </h:panelGroup>
            </f:facet>
            <h:form id="formConfirmaEstorno">
                <rich:panel>
                    <h:outputText rendered="#{not empty MovContaControle.movContaVO.conjuntoPagamento}" 
                                  styleClass="tituloDemonstrativo" value="Esta conta foi paga em conjunto com outras contas, que terão seus pagamentos estornados automaticamente. Confirma o estorno da Data de Quitação?"/>
                    <h:outputText rendered="#{empty MovContaControle.movContaVO.conjuntoPagamento}" 
                                  styleClass="tituloDemonstrativo"  value="Confirma o estorno da Data de Quitação?"/>
                </rich:panel>
                <center>
                    <a4j:commandButton id="sim"
                                       action="#{MovContaControle.estornarPagamento}"
                                       oncomplete="Richfaces.hideModalPanel('modalConfirmaEstorno');"
                                       image="/imagens/botaoSim.png" 
                                       reRender="formLanc"
                                       styleClass="botoes">
                    </a4j:commandButton>
                    <rich:spacer width="30px;"></rich:spacer>
                    <a4j:commandButton id="nao"  
                    				   status="statusInComponent"
                    				   image="/imagens/botaoNao.png"
                                       oncomplete="Richfaces.hideModalPanel('modalConfirmaEstorno');">
					</a4j:commandButton>
              	</center>
            </h:form>
        </rich:modalPanel>
    </a4j:outputPanel>
