<%--
  Created by IntelliJ IDEA.
  User: estulano
  Date: 27/06/2016
  Time: 16:48
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<style>
    .line-space{
        margin-bottom: 10px;
    }
</style>

<h:panelGroup id="containerOutrosPadroes" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabecalhoOutrosPadroes" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Outros padr�es</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-md-12">
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Segue abaixo, outros itens relevantes ao padr�o de qualidade que dever�o ser seguidos tanto pela equipe de desenvolvimento quanto por toda a equipe de qualidade da Pacto.
                            Se o que estiver aqui n�o for seguido no ato da implementa��o, a equipe de qualidade ter� total poder de reprovar o teste dessa implementa��o pelo fato de que ser� ser considerado como Fora do Padr�o do nosso sistema e consequentemente como uma FALHA.
                        </span>
                    </div>
                    <div class="col-md-12 line-space " style="margin-top: 30px">
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">
                          Cria��o de novas telas em novos recursos
                        </span>
                        <span class="texto-size-14 texto-cor-cinza texto-font" style="display: block">
                            Foi criado um recurso novo no sistema?
                        </span>
                        <span class="texto-size-14 texto-cor-cinza texto-font" style="display: block">
                            Ent�o todas as telas envolvendo este recurso novo dever�o ser padronizadas, isto para depois n�o faltar um bot�o na tela B sendo que na tela A tem o bot�o.
                        </span>
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                            Isto evita, e muito, futuras reclama��es e solicita��es de clientes para a Pacto.
                        </span>
                    </div>
                    <div class="col-md-12 line-space " style="margin-top: 30px">
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">
                          Valida��o de exclus�o de registro com refer�ncia em outras tabelas
                        </span>
                        <span class="texto-size-14 texto-cor-cinza texto-font" style="display: block">
                            Tentou excluir algo e foi barrado devido a v�nculos com aquele registro?
                        </span>
                        <span class="texto-size-14 texto-cor-cinza texto-font" style="display: block">
                          As mensagens que n�o permitem excluir algo por chave estrangeira devem estar claras ao usu�rio, informando sempre o motivo pelo qual ele n�o conseguiu excluir.
                        </span>
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                            Um exemplo de como tem que ficar, � ao tentar excluir uma turma que tem alunos lan�ados nela, onde atualmente exibe uma mensagem de refer�ncia tratada ao usu�rio.
                        </span>
                    </div>
                    <div class="col-md-12 line-space " style="margin-top: 30px">
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">
                          Alinhamento de label em rela��o ao seu respectivo campo
                        </span>
                        <span class="texto-size-14 texto-cor-cinza texto-font" style="display: block">
                            Todos os novos campos que possuem label (t�tulo) devem ter um padr�o que � a label � esquerda do respectivo campo, sendo assim, o campo ficando a direita e a label � esquerda do seu respectivo campo.
                        </span>
                    </div>
                </div>
            </div>
        </div>
</h:panelGroup>