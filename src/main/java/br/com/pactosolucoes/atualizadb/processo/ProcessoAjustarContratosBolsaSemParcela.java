package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoAjustarContratosBolsaSemParcela {

    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "abfitnesssaogoncalo";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            ajustarContratosBolsaSemParcela(c);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoAjustarContratosBolsaSemParcela.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarContratosBolsaSemParcela(Connection con) {
        try {
            Uteis.logarDebug("INÍCIO | ProcessoAjustarContratosBolsaSemParcela");
            StringBuilder sqlSelectContratoBolsaSemParcela = new StringBuilder();
            sqlSelectContratoBolsaSemParcela.append("SELECT\n");
            sqlSelectContratoBolsaSemParcela.append("\tc.responsavelcontrato AS responsavelContrato,\n");
            sqlSelectContratoBolsaSemParcela.append("\tc.codigo AS codigoContrato,\n");
            sqlSelectContratoBolsaSemParcela.append("\tc.datalancamento::date AS dataLancamentoContrato,\n");
            sqlSelectContratoBolsaSemParcela.append("\tc.regimerecorrencia AS regimeRecorrencia,\n");
            sqlSelectContratoBolsaSemParcela.append("\tc.empresa AS codigoEmpresaContrato,\n");
            sqlSelectContratoBolsaSemParcela.append("\tc.pessoa AS codigoPessoaContrato,\n");
            sqlSelectContratoBolsaSemParcela.append("\tp.nome AS nomePessoaContrato\n");
            sqlSelectContratoBolsaSemParcela.append("FROM contrato c\n");
            sqlSelectContratoBolsaSemParcela.append("INNER JOIN pessoa p ON p.codigo = c.pessoa\n");
            sqlSelectContratoBolsaSemParcela.append("INNER JOIN empresa e ON e.codigo = c.empresa\n");
            sqlSelectContratoBolsaSemParcela.append("INNER JOIN contratocondicaopagamento ccp ON ccp.contrato = c.codigo\n");
            sqlSelectContratoBolsaSemParcela.append("INNER JOIN condicaopagamento cpg ON cpg.codigo = ccp.condicaopagamento\n");
            sqlSelectContratoBolsaSemParcela.append("WHERE NOT EXISTS (SELECT codigo FROM movparcela WHERE contrato = c.codigo)\n");
            sqlSelectContratoBolsaSemParcela.append("AND c.situacao = 'AT'\n");
            sqlSelectContratoBolsaSemParcela.append("AND c.situacaocontrato <> 'TF'\n");
            sqlSelectContratoBolsaSemParcela.append("AND (c.bolsa OR c.valorfinal = 0.0)\n");
            sqlSelectContratoBolsaSemParcela.append("ORDER BY c.datalancamento;\n");
            try (java.sql.Statement stm = con.createStatement()) {
                try (java.sql.ResultSet rs = stm.executeQuery(sqlSelectContratoBolsaSemParcela.toString())) {
                    while (rs.next()) {
                        int responsavelContrato = rs.getInt("responsavelContrato");
                        int codigoContrato = rs.getInt("codigoContrato");
                        Date dataLancamentoContrato = rs.getDate("dataLancamentoContrato");
                        boolean regimeRecorrencia = rs.getBoolean("regimeRecorrencia");
                        int codigoEmpresaContrato = rs.getInt("codigoEmpresaContrato");
                        int codigoPessoaContrato = rs.getInt("codigoPessoaContrato");
                        String nomePessoaContrato = rs.getString("nomePessoaContrato");

                        StringBuilder sqlInsertMovParcela = new StringBuilder();
                        sqlInsertMovParcela.append("INSERT INTO movparcela (descricao,imprimirboletoparcela,utilizaconvenio,percentualjuro,percentualmulta,responsavel,valorparcela,contrato,situacao,datavencimento,dataregistro,nrtentativas,regimerecorrencia,empresa,pessoa,movimentocc,reagendada,parceladcc,nrtentativasprocessoretentativa,parcelasrenegociadas,planopersonal,incluidaspc,dataalteracaosituacao)");
                        sqlInsertMovParcela.append("VALUES ('PARCELA 1'");
                        sqlInsertMovParcela.append(",false");
                        sqlInsertMovParcela.append(",false");
                        sqlInsertMovParcela.append(",0.0");
                        sqlInsertMovParcela.append(",0.0");
                        sqlInsertMovParcela.append(",").append(responsavelContrato);
                        sqlInsertMovParcela.append(",0.0");
                        sqlInsertMovParcela.append(",").append(codigoContrato);
                        sqlInsertMovParcela.append(",'PG'");
                        sqlInsertMovParcela.append(",'").append(Uteis.getDataJDBC(dataLancamentoContrato)).append("'");
                        sqlInsertMovParcela.append(",'").append(Uteis.getDataJDBC(dataLancamentoContrato)).append("'");
                        sqlInsertMovParcela.append(",0");
                        sqlInsertMovParcela.append(",").append(regimeRecorrencia);
                        sqlInsertMovParcela.append(",").append(codigoEmpresaContrato);
                        sqlInsertMovParcela.append(",").append(codigoPessoaContrato);
                        sqlInsertMovParcela.append(",false");
                        sqlInsertMovParcela.append(",false");
                        sqlInsertMovParcela.append(",false");
                        sqlInsertMovParcela.append(",0");
                        sqlInsertMovParcela.append(",''");
                        sqlInsertMovParcela.append(",0");
                        sqlInsertMovParcela.append(",false");
                        sqlInsertMovParcela.append(",'").append(Uteis.getDataJDBC(dataLancamentoContrato)).append("')");
                        try (PreparedStatement sqlInserirParcela = con.prepareStatement(sqlInsertMovParcela.toString())) {
                            sqlInserirParcela.execute();
                        }
                        try (java.sql.ResultSet rsParcelaGerada = stm.executeQuery("SELECT MAX(codigo) AS codigo FROM movparcela")) {
                            int codigoParcelaGerada = rsParcelaGerada.next() ? rsParcelaGerada.getInt("codigo") : 0;
                            if (!UteisValidacao.emptyNumber(codigoParcelaGerada)) {
                                StringBuilder sqlInsertReciboPagamento = new StringBuilder();
                                sqlInsertReciboPagamento.append("INSERT INTO recibopagamento (valortotal,pessoapagador,nomepessoapagador,responsavellancamento,contrato,data,empresa,integrado) ");
                                sqlInsertReciboPagamento.append("VALUES (0.0");
                                sqlInsertReciboPagamento.append(",").append(codigoPessoaContrato);
                                sqlInsertReciboPagamento.append(",'").append(nomePessoaContrato).append("'");
                                sqlInsertReciboPagamento.append(",").append(responsavelContrato);
                                sqlInsertReciboPagamento.append(",").append(codigoContrato);
                                sqlInsertReciboPagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoContrato)).append("'");
                                sqlInsertReciboPagamento.append(",").append(codigoEmpresaContrato);
                                sqlInsertReciboPagamento.append(",false)");
                                try (PreparedStatement sqlInserirReciboPagamento = con.prepareStatement(sqlInsertReciboPagamento.toString())) {
                                    sqlInserirReciboPagamento.execute();
                                }
                                try (java.sql.ResultSet rsReciboGerado = stm.executeQuery("SELECT MAX(codigo) AS codigo FROM recibopagamento")) {
                                    int codigoReciboGerado = rsReciboGerado.next() ? rsReciboGerado.getInt("codigo") : 0;
                                    if (!UteisValidacao.emptyNumber(codigoReciboGerado)) {
                                        try (java.sql.ResultSet rsFormaPagamentoAVista = stm.executeQuery("SELECT codigo as codigo FROM FormaPagamento WHERE upper(tipoFormaPagamento) LIKE ('AV') AND ativo AND NOT somentefinanceiro ORDER BY codigo LIMIT 1")) {
                                            int codigoFormaPagamento = rsFormaPagamentoAVista.next() ? rsFormaPagamentoAVista.getInt("codigo") : 0;

                                            StringBuilder sqlInsertMovpagamento = new StringBuilder();
                                            sqlInsertMovpagamento.append("INSERT INTO movpagamento (responsavelpagamento");
                                            sqlInsertMovpagamento.append(",nrparcelacartaocredito");
                                            sqlInsertMovpagamento.append(",movpagamentoescolhida");
                                            sqlInsertMovpagamento.append(",nomepagador");
                                            sqlInsertMovpagamento.append(",formapagamento");
                                            sqlInsertMovpagamento.append(",valor");
                                            sqlInsertMovpagamento.append(",datalancamento");
                                            sqlInsertMovpagamento.append(",datapagamento");
                                            sqlInsertMovpagamento.append(",pessoa");
                                            sqlInsertMovpagamento.append(",dataquitacao");
                                            sqlInsertMovpagamento.append(",recibopagamento");
                                            sqlInsertMovpagamento.append(",empresa");
                                            sqlInsertMovpagamento.append(",observacao");
                                            sqlInsertMovpagamento.append(",autorizacaocartao");
                                            sqlInsertMovpagamento.append(",credito");
                                            sqlInsertMovpagamento.append(",valortotal");
                                            sqlInsertMovpagamento.append(",nsu");
                                            sqlInsertMovpagamento.append(",depositocc");
                                            sqlInsertMovpagamento.append(",datapagamentooriginal");
                                            sqlInsertMovpagamento.append(",usarparceirofidelidade");
                                            sqlInsertMovpagamento.append(",tabelaparceirofidelidade");
                                            sqlInsertMovpagamento.append(",multiplicadorparceirofidelidade");
                                            sqlInsertMovpagamento.append(",tipopontoparceirofidelidade");
                                            sqlInsertMovpagamento.append(",pontosparceirofidelidade");
                                            sqlInsertMovpagamento.append(",cpfparceirofidelidade");
                                            sqlInsertMovpagamento.append(",parceirofidelidadeprocessado");
                                            sqlInsertMovpagamento.append(",codigoexternoprodutoparceirofidelidade");
                                            sqlInsertMovpagamento.append(",senhaparceirofidelidade");
                                            sqlInsertMovpagamento.append(",enviadoconciliadora");
                                            sqlInsertMovpagamento.append(",respostarequisicaopinpad");
                                            sqlInsertMovpagamento.append(",numerounicotransacao)");
                                            sqlInsertMovpagamento.append("VALUES (").append(responsavelContrato);
                                            sqlInsertMovpagamento.append(",0");
                                            sqlInsertMovpagamento.append(",true");
                                            sqlInsertMovpagamento.append(",'").append(nomePessoaContrato).append("'");
                                            sqlInsertMovpagamento.append(",").append(codigoFormaPagamento);
                                            sqlInsertMovpagamento.append(",0.0");
                                            sqlInsertMovpagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoContrato)).append("'");
                                            sqlInsertMovpagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoContrato)).append("'");
                                            sqlInsertMovpagamento.append(",").append(codigoPessoaContrato);
                                            sqlInsertMovpagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoContrato)).append("'");
                                            ;
                                            sqlInsertMovpagamento.append(",").append(codigoReciboGerado);
                                            sqlInsertMovpagamento.append(",").append(codigoEmpresaContrato);
                                            sqlInsertMovpagamento.append(",''");
                                            sqlInsertMovpagamento.append(",''");
                                            sqlInsertMovpagamento.append(",false");
                                            sqlInsertMovpagamento.append(",0.0");
                                            sqlInsertMovpagamento.append(",''");
                                            sqlInsertMovpagamento.append(",false");
                                            sqlInsertMovpagamento.append(",'").append(Uteis.getDataJDBC(dataLancamentoContrato)).append("'");
                                            ;
                                            sqlInsertMovpagamento.append(",false");
                                            sqlInsertMovpagamento.append(",0");
                                            sqlInsertMovpagamento.append(",0.0");
                                            sqlInsertMovpagamento.append(",0");
                                            sqlInsertMovpagamento.append(",0");
                                            sqlInsertMovpagamento.append(",''");
                                            sqlInsertMovpagamento.append(",false");
                                            sqlInsertMovpagamento.append(",''");
                                            sqlInsertMovpagamento.append(",''");
                                            sqlInsertMovpagamento.append(",false");
                                            sqlInsertMovpagamento.append(",''");
                                            sqlInsertMovpagamento.append(",'')");
                                            try (PreparedStatement sqlInserirMovpagamento = con.prepareStatement(sqlInsertMovpagamento.toString())) {
                                                sqlInserirMovpagamento.execute();
                                            }
                                            try (java.sql.ResultSet rsMovpagamentoGerado = stm.executeQuery("SELECT MAX(codigo) AS codigo FROM movpagamento")) {
                                                int codigoMovpagamentoGerado = rsMovpagamentoGerado.next() ? rsMovpagamentoGerado.getInt("codigo") : 0;
                                                if (!UteisValidacao.emptyNumber(codigoMovpagamentoGerado)) {
                                                    StringBuilder sqlInsertPagamentoMovParcela = new StringBuilder();
                                                    sqlInsertPagamentoMovParcela.append("INSERT INTO pagamentomovparcela (valorpago");
                                                    sqlInsertPagamentoMovParcela.append(",recibopagamento");
                                                    sqlInsertPagamentoMovParcela.append(",movparcela");
                                                    sqlInsertPagamentoMovParcela.append(",movpagamento)");
                                                    sqlInsertPagamentoMovParcela.append("VALUES (0.0");
                                                    sqlInsertPagamentoMovParcela.append(",").append(codigoReciboGerado);
                                                    sqlInsertPagamentoMovParcela.append(",").append(codigoParcelaGerada);
                                                    sqlInsertPagamentoMovParcela.append(",").append(codigoMovpagamentoGerado).append(")");
                                                    try (PreparedStatement sqlInserirPagamentoMovparcela = con.prepareStatement(sqlInsertPagamentoMovParcela.toString())) {
                                                        sqlInserirPagamentoMovparcela.execute();
                                                    }

                                                    try (java.sql.ResultSet rsMovprodutoContrato = stm.executeQuery("SELECT codigo as codigo FROM movproduto WHERE contrato = " + codigoContrato)) {
                                                        while(rsMovprodutoContrato.next()){
                                                            int codigoMovproduto = rsMovprodutoContrato.getInt("codigo");
                                                            StringBuilder sqlInsertMovProdutoParcela = new StringBuilder();
                                                            sqlInsertMovProdutoParcela.append("INSERT INTO movprodutoparcela (valorpago,movparcela,recibopagamento,movproduto)");
                                                            sqlInsertMovProdutoParcela.append(" VALUES (0.0");
                                                            sqlInsertMovProdutoParcela.append(",").append(codigoParcelaGerada);
                                                            sqlInsertMovProdutoParcela.append(",").append(codigoReciboGerado);
                                                            sqlInsertMovProdutoParcela.append(",").append(codigoMovproduto).append(")");
                                                            try (PreparedStatement sqlInserirMovprodutoParcela = con.prepareStatement(sqlInsertMovProdutoParcela.toString())) {
                                                                sqlInserirMovprodutoParcela.execute();
                                                            }
                                                            StringBuilder sqlUpdateMovproduto = new StringBuilder();
                                                            sqlUpdateMovproduto.append("UPDATE movproduto SET situacao = 'PG' WHERE codigo = ").append(codigoMovproduto);
                                                            try (PreparedStatement psSqlUpdateMovproduto = con.prepareStatement(sqlUpdateMovproduto.toString())) {
                                                                psSqlUpdateMovproduto.execute();
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Uteis.logarDebug("FIM | ProcessoAjustarContratosBolsaSemParcela");
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoAjustarContratosBolsaSemParcela - " + ex.getMessage());
        }
    }

}
