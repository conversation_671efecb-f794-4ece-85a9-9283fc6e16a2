package controle.financeiro;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import negocio.comuns.financeiro.CupomFiscalVO;
import negocio.comuns.financeiro.StatusImpressaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.financeiro.MovPagamento;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import negocio.comuns.utilitarias.ControleConsulta;

public class CupomFiscalControle extends SuperControle {

    private List<CupomFiscalVO> lista;
    private ConsultaCupomTO filtro;
    private List<SelectItem> comboStatus;
    private String msgTotal;

    public CupomFiscalControle() {
        setControleConsulta(new ControleConsulta());
        getFiltro();
    }

    /**
     * @return the lista
     * @throws SQLException
     */
    public List<CupomFiscalVO> getLista() throws Exception {
        if (lista == null) {
            lista = new ArrayList<CupomFiscalVO>();
        }
        return lista;
    }

    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigoRecibo", "Código Recibo"));
        itens.add(new SelectItem("matricula", "Matrícula"));
        itens.add(new SelectItem("nomePessoa", "Nome Pessoa"));
        return itens;
    }

    /**
     * @param lista
     *            the lista to set
     */
    public void setLista(List<CupomFiscalVO> lista) {
        this.lista = lista;
    }

    /**
     * @return the filtro
     */
    public ConsultaCupomTO getFiltro() {
        if (filtro == null) {
            filtro = new ConsultaCupomTO();
            filtro.setDataImpressaoIni(Calendario.hoje());
            filtro.setDataImpressaoFim(Calendario.hoje());
            filtro.setDataVendaIni(Calendario.hoje());
            filtro.setDataVendaFim(Calendario.hoje());
            filtro.setMatricula("");
            filtro.setNome("");
        }
        return filtro;
    }

    /**
     * @param filtro
     *            the filtro to set
     */
    public void setFiltro(ConsultaCupomTO filtro) {
        this.filtro = filtro;
    }

    /**
     * @return the comboStatus
     */
    public List<SelectItem> getComboStatus() {
        if (comboStatus == null) {
            comboStatus = new ArrayList<SelectItem>();
            StatusImpressaoEnum[] values = StatusImpressaoEnum.values();
            for (int i = 0; i < values.length; i++) {
                StatusImpressaoEnum status = values[i];
                // os cupons cancelados são removidos do sistema
                // por isso não deve existir essa opção de filtro
                if (status != StatusImpressaoEnum.CANCELADO) {
                    comboStatus.add(new SelectItem(status.getCodigo(), status.getDescricao()));
                }
            }
        }
        return comboStatus;
    }

    /**
     * @param comboStatus
     *            the comboStatus to set
     */
    public void setComboStatus(List<SelectItem> comboStatus) {
        this.comboStatus = comboStatus;
    }

    /**
     * @return the msgTotal
     */
    public String getMsgTotal() {
        if (msgTotal == null) {
            msgTotal = "";
        }
        return msgTotal;
    }

    /**
     * @param msgTotal
     *            the msgTotal to set
     */
    public void setMsgTotal(String msgTotal) {
        this.msgTotal = msgTotal;
    }

    public void consultarCupons(ActionEvent e) {
        try {
            getFacade().getCupomFiscal().setCon(
                    ((MovPagamento) getFacade().getMovPagamento()).getCon());
            montarFiltros();
            // consulta o cupom fiscal associado aquele recibo
            Map<String, Object> consultaCupons = getFacade().getCupomFiscal().consultaCupons(getFiltro());
            this.setLista((List<CupomFiscalVO>) consultaCupons.get("lista"));

            Integer totalCupons = (Integer) consultaCupons.get("totalCupons");
            Double totalValor = (Double) consultaCupons.get("totalValor");

            StringBuffer sb = new StringBuffer();
            sb.append("Foram consultados " + totalCupons
                    + " cupons. Totalizando um valor de "
                    + Formatador.formatarValorMonetario(totalValor));
            this.setMsgTotal(sb.toString());
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            setMensagemDetalhada(ex.getMessage());
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getCupomFiscal().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void limparDataImpressao() {
        filtro.setDataImpressaoIni(null);
        filtro.setDataImpressaoFim(null);
    }

    public void limparDataVenda() {
        filtro.setDataVendaIni(null);
        filtro.setDataVendaFim(null);
    }

    public void montarFiltros() throws ValidacaoException {
        filtro.setMatricula("");
        filtro.setNome("");
        filtro.setCodigoRecibo(0);
        if (getControleConsulta().getCampoConsulta().equals("codigoRecibo") && !getControleConsulta().getValorConsulta().isEmpty()) {
            Integer codigoRecibo;
            try {
                codigoRecibo = new Integer(controleConsulta.getValorConsulta());
            } catch (NumberFormatException nfe) {
                throw new ValidacaoException("Erro ao tentar consultar pelo código do recibo: " + controleConsulta.getValorConsulta() + ". Informe apenas números");
            }
            getFiltro().setCodigoRecibo(codigoRecibo);
        } else if (getControleConsulta().getCampoConsulta().equals("matricula") && !getControleConsulta().getValorConsulta().isEmpty()) {
            Integer matricula;
            try {
                matricula = new Integer(controleConsulta.getValorConsulta());
            } catch (NumberFormatException nfe) {
                throw new ValidacaoException("Erro ao tentar consultar pela matrícula: " + controleConsulta.getValorConsulta() + ". Informe apenas números");
            }
            getFiltro().setMatricula(controleConsulta.getValorConsulta());
        } else if (getControleConsulta().getCampoConsulta().equals("nomePessoa") && !getControleConsulta().getValorConsulta().isEmpty()) {
            getFiltro().setNome(controleConsulta.getValorConsulta());
        }
    }
}
