package negocio.facade.jdbc.oamd;

public class EmpresaRedeDTO {
    private String chave;
    private String chaveRede;
    private Integer idRede;
    private Boolean ativa;
    private String zwUrl;
    private String planoMsUrl;
    private String produtoMsUrl;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getChaveRede() {
        return chaveRede;
    }

    public void setChaveRede(String chaveRede) {
        this.chaveRede = chaveRede;
    }

    public Integer getIdRede() {
        return idRede;
    }

    public void setIdRede(Integer idRede) {
        this.idRede = idRede;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public String getZwUrl() {
        return zwUrl;
    }

    public void setZwUrl(String zwUrl) {
        this.zwUrl = zwUrl;
    }

    public String getPlanoMsUrl() {
        return planoMsUrl;
    }

    public void setPlanoMsUrl(String planoMsUrl) {
        this.planoMsUrl = planoMsUrl;
    }

    public String getProdutoMsUrl() {
        return produtoMsUrl;
    }

    public void setProdutoMsUrl(String produtoMsUrl) {
        this.produtoMsUrl = produtoMsUrl;
    }
}
