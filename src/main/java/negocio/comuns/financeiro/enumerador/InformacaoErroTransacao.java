package negocio.comuns.financeiro.enumerador;

public enum InformacaoErroTransacao {

    INFO_0(0, "Transação Autorizada", "Autorizada.", 1),

    INFO_01(1, "Venda não autorizada.", "O Banco emissor do cartão não autoriza compra, " +
            "enquanto não for realizado contato com banco. " +
            "Neste caso o cliente está realizando uma compra " +
            "com valor acima do que está acostumado, " +
            "por questão de segurança o banco não autoriza.", 5),

    INFO_02(2, "Transação negada. Ou Falha de comunicação com o banco emissor do cartão",
            "Afiliação do estabelecimento está com status encerrado na operadora." +
                    "Ou Verificar com a operadora o que ocorreu.", 2),

    INFO_03(3, "Não foi encontrada transação para o Tid '***************'.",
            "Este erro pode ser:\n" +
                    "- número de parcelas ultrapassa o permitido.\n" +
                    "- código de segurança inválido\n" +
                    "- número do cartão inválido\n" +
                    "- instabilidade no sistema da Cielo.", 5),

    INFO_04(4, "Erro desconhecido", "Por favor, entre em contato com a Cielo.", 1),

    INFO_05(5, "Não Autorizado - 5 - Não foi possível processar a transação. \n " +
            "ou Venda não autorizada. Contate o emissor do seu cartão.", "Este erro pode ocorrer quando sua empresa utiliza a " +
            "Conectividade Cielo MoSet (semelhante ao TEF Discado). " +
            "Caso esta indisponibilidade momentânea se torne persistente, " +
            "entre em contato com a Cielo. \n" +
            "ou Caso a Conectividade utilizada pela sua empresa seja TEF Dedicado, " +
            "significa que O banco emissor do cartão não autorizou a venda.", 3),

    INFO_07(7, "Transação negada - Venda não autorizada.", "O banco emissor do cartão não autorizou a venda.", 1),

    INFO_08(8, "Código de segurança inválido", "Código de segurança inválido. " +
            "Baixe o slide presente no Wiki e veja como solucionar esta situação.\n " +
            "Erro genérico, faça contato com a Cielo fornecendo valor para ser tratado.", 2),

    INFO_10(10, "Não é permitido o envio do cartão", "O estabelecimento não possui liberação para enviar o cartão, " +
            "a chave foi gerada na Cielo para a modalidade Buy Page Cielo " +
            "e o meio de pagamento em que se está realizando o teste é Buy Page Loja. " +
            "Caso a homologação tenha sido solicitada para Buy Page Loja " +
            "é necessário entrar em contato com a Operadora para que eles " +
            "realizem um ajuste na configuração.", 6),

    INFO_11(11, "Venda com Parcelado tipo '2' não habilitado.",
            "Verificar com o operadora " +
                    "qual limite de parcelamento do estabelecimento no Parcelado Loja.", 2),

    INFO_13(13, "Venda não autorizada.",
            "Verificar o valor do pedido e a forma de pagamento," +
                    " pois para compras parceladas o valor mínimo do pedido deverá ser R$ 10,00(R$ 5,00 por parcela).", 2),

    INFO_41(41, "Venda não autorizada. Contate o emissor do seu cartão.",
            "Cliente deve entrar em contato com o banco emissor " +
                    "do cartão para verificar por que o banco não está autorizando a compra.", 2),

    INFO_51(51, "Transação negada - Venda não autorizada. Ou Falha na comunicação entre a Cielo e a operadora de cartão.",
            "Cliente deve entrar em contato com o banco para verificar por que o " +
                    "banco não está autorizando a compra. Ou Ligar na Cielo e reiniciar o serviço.", 2),

    INFO_54(54, "Cartão vencido ou data de vencimento incorreta",
            "Caso os dados informados estejam corretos, " +
                    "o cliente deve entrar em contato com o banco para verificar se cartão ainda é valido." +
                    "Os dados fornecidos pelo cliente estão incorretos.", 2),

    INFO_78(78, "Transação negada - Venda não autorizada.",
            "Cartão bloqueado. " +
                    "O portador do cartão não desbloqueou o cartão para poder utilizá-lo.", 2),

    INFO_81(81, "Transação negada - Por favor, refaça a transação.",
            "Cartão utilizado não foi emitido pelo Bradesco.", 1),

    INFO_99(99, "Sistema do banco temporariamente fora de operação, por favor tente mais tarde.",
            "Sistema do banco temporariamente fora de operação. \n" +
                    "Também verificar: \n" +
                    "- Se o estabelecimento tem liberado na operadora o meio de pagamento Visa Electron. \n" +
                    "    * Para verificar se o estabelecimento tem liberado o meio de pagamento Visa Electron, \n" +
                    "      realize contato com a Cielo, suporte Web.\n" +
                    "- Se o meio de pagamento Visa Electron estiver liberado, \n" +
                    "  neste mesmo contato verifique se o estabelecimento tem habilitado(na operadora) \n" +
                    "  o parcelamento realizado no pedido, pois possivelmente o parcelamento escolhido \n" +
                    "  não está ativo para o Estabelecimento.", 8),

    INFO_110(110, "Não foi possível processar a transação. Sistema sem comunicação. Tente mais tarde.",
            "Erro 110 ocorre quanto são utilizados cartões de outros bancos. " +
                    "Para e-commerce, a Cielo aceita somente cartões do Banco Bradesco.", 2),

    INFO_196(196, "Venda não autorizada.",
            "Realizada Sonda e o retorno da operadora é: " +
                    "ars=transação finalizada antes de iniciar autorização. " +
                    "Alguma informação do cartão não foi informada corretamente no momento da compra. " +
                    "Cartão utilizado para realizar compra é cartão de testes da Cielo " +
                    "(cliente conseguiu o cartão de alguma forma e tentou realizar compra)", 5),

    INFO_213(213, "Não foi possível processar a transação " +
            "ou Não foi possível processar a transação. Identificador da Transação TID duplicado.",
            "Falha de comunicação com o banco emissor do cartão. \n" +
                    "Verificar com a operadora o que ocorreu. " +
                    "Ou Por alguma razão o pop-up da operadora abriu em segundo plano." +
                    "Ou o pop-up não abriu porque tem bloqueador de pop-up no navegador e " +
                    "sem perceber o cliente clica em 'Clique Aqui' no iPAGARE para forçar a abertura do pop-up, " +
                    "o mesmo TID (identificação única junto a VISA enviado na primeira tentativa) é reenviado, " +
                    "resultando na mensagem em questão (TID duplicado).", 5),

    INFO_215(215, "Transação não finalizada.",
            "Possíveis dificuldades com pop-up bloqueado no navegador do cliente.", 1),

    INFO_244(244, "Não foi possível processar a transação.",
            "Cartão utilizado pelo cliente não faz parte da rede autenticada Cielo, " +
                    "esse erro ocorre com meio de pagamento Visa Electron " +
                    "no caso o cartão que está sendo usado não é do banco emissor Bradesco.", 3),

    INFO_997(997, "Não foi possível processar a transação.",
            "Esse erro geralmente ocorre por falha na autenticação com banco, " +
                    "por este motivo podem aparecer mais de uma tentativa sem sucesso e outra finalizada. " +
                    "Também ocorre quando o cliente digita os dados do cartão errado.", 3),

    INFO_5115(5115, "Não foi possível processar a transação.",
            "Tentativa de pagamento com cartão emitido no exterior " +
                    "que não autenticou com sucesso no Internet Banking do banco emissor.", 2),

    INFO_203(203, "Falha no Banco de Dados", "Entre em contato com o Suporte Técnico", 1),

    INFO_91(91, "Instuição Destino Temporariamene Fora de Serviço.", "Repita a transação.", 1),

    INFO_84(84, "Log-On", "Refaça Transação", 1),

    INFO_82(82, "Chave de Criptografia Inválida", "Verifique a chave e repita a operação", 1),

    INFO_43(43, "Problema identificação cartão", "Tente novamente", 1),

    INFO_25(25, "Registro não Encontrado", "Refaça transação", 1),

    INFO_14(14, "Cartão Inválido", "Entre em contato com a operadora do cartão.", 1),

    INFO_995(995, "Não foi possível processar a transação",
            "Cliente utilizando cartão que não é do Bradesco. " +
                    "Este erro ocorre pois autenticação não é finalizada no ambiente da operadora." +
                    "Cartão é internacional e o banco emissor do cartão não participa da rede Verified by Visa." +
                    "No caso do VBV a autenticação é realizada no ambiente da operadora e " +
                    "dependendo do banco emissor do cartão é necessário passar dados do cartão " +
                    "ou do cliente para finalizar pedido. " +
                    "Cliente está tentando realizar a compra parcelada com cartão internacional, " +
                    "para cartões internacionais não é possível comprar parcelado somente a vista. " +
                    "Este erro ocorre tanto para a tecnologia MOSET e Verified By Visa. " +
                    "Cliente estava utilizando cartão de débito como crédito.", 9),

    INFO_999(999, "Transação negada - Venda não autorizada.",
            "Cartão utilizado não foi emitido pelo Bradesco. " +
                    "Pagamento não autorizado pelo banco emissor do cartão. " +
                    "Este erro também pode ocorrer quando a página " +
                    "onde é digitado os dados do cartão fica muito tempo aberta, " +
                    "aguardando a digitação dos dados.", 5),

    INFO_57(57, "Problema com o cartão ou Falha na comunicação com a operadora. Ou Venda não autorizada",
            "Ligar na Cielo para provavelmente reiniciar o serviço." +
                    "Ou Venda não autorizada pelo emissor do cartão. " +
                    "Cartão utilizado não faz parte da rede Verified by Visa. " +
                    "Sistema de prevenção do banco não autorizou a compra, " +
                    "neste caso o cliente deverá realizar contato com banco emissor do cartão " +
                    "e informar que está tentando realizar uma compra no valor R$XXX " +
                    "e não está sendo autorizada.", 4),

    INFO_PADRAO(999999, "Erro desconhecido", "Por favor, entre em contato com a Cielo.", 1);

    private int codigoErro;
    private String erro;
    private String descricao;

    private int alturaLinha = 35;
    private int quantidadeLinhas = 1;

    InformacaoErroTransacao(int codigoErro, String erro, String descricao, int quantidadeLinhas) {
        this.codigoErro = codigoErro;
        this.erro = erro;
        this.descricao = descricao;
        this.quantidadeLinhas = quantidadeLinhas;
    }

    public static InformacaoErroTransacao getTipoInformacaoErroTransacao(int codigo) {
        for (InformacaoErroTransacao to : InformacaoErroTransacao.values()) {
            if (to.getCodigoErro() == codigo) {
                return to;
            }
        }
        return INFO_PADRAO;
    }

    public int getCodigoErro() {
        return codigoErro;
    }

    public void setCodigoErro(int codigoErro) {
        this.codigoErro = codigoErro;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getAlturaLinha() {
        return alturaLinha;
    }

    public void setAlturaLinha(int alturaLinha) {
        this.alturaLinha = alturaLinha;
    }

    public String getEstilo() {
        return "width:550px; height:" + (alturaLinha * this.quantidadeLinhas) + "px";
    }

    public int getQuantidadeLinhas() {
        return quantidadeLinhas;
    }

    public void setQuantidadeLinhas(int quantidadeLinhas) {
        this.quantidadeLinhas += quantidadeLinhas;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }
}
